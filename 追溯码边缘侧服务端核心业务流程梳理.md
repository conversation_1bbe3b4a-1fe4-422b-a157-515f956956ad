# 追溯码边缘侧服务端核心业务流程梳理

## 1. 业务概述

追溯码边缘侧服务端是用于处理门诊和住院的药品与追溯码进行关联业务的核心服务。

核心功能包含：门诊发药查询、住院发药查询、药品和追溯码的匹配关联上传、退药查询、退药还原追溯码的使用状态、定时上传3505报文。

次要功能包含：发药单查询、扫码任务查询、取消扫码任务。

掌握门诊和住院发药、退药、定时任务上传3505即可掌握80%的业务流程。


## 2. 业务流程详述

### 2.1 门诊发药查询（也可查退药）

#### 概述
门诊发药查询接口用于前端在药房窗口检索门诊处方信息，以便执行发药或退药操作。通过`fg_dps`参数可动态区分查询类型：
- `fg_dps = 0`：查询待发药数据（默认）。
- `fg_dps = 1`：查询退药数据（不做扫码任务过滤）。

#### 关键业务规则
- 同一处方可重复查询，但对同一药品不可重复发药。
- 发药查询需过滤已完成扫码任务的药品行，退药查询则完整返回原始数据。
- 查询结果必须包含药品最小包装信息（minDoseCount）和追溯码标志（是否强制扫码）。
- 支持按患者信息、处方号、开方科室、时间段等组合条件查询。

#### 业务流程生命周期
1. 前端输入查询条件并决定查询类型（发药/退药）。
2. 后端验证权限后调用`FuYangRenYiService.queryOutpatientPrescription`。
3. Service层根据`fg_dps`组装查询条件并访问HIS接口或HIS视图数据。
4. 解析HIS返回数据，应用业务过滤规则。
5. 封装`OutpatientPrescriptionResponseVo`返回前端。

##### 阶段1：前端发起查询
- **步骤描述**：前端构造`OutpatientPrescriptionQueryDto`并提交到`/queryOutpatientPrescription`。
- **目的**：获取指定患者的处方明细与药品信息。
- **关联性**：`fg_dps`决定后续过滤逻辑。
- **技术细节**：使用POST；token校验由`@SaasAuthorizationVerify`完成。

##### 阶段2：Service组装查询条件
- **步骤描述**：Service依据`fg_dps`及其他筛选项拼接HIS请求参数。
- **目的**：保证查询粒度和业务场景匹配。
- **关联性**：`fg_dps=0`时需要额外排除已采集完的药品。
- **技术细节**：调用HIS REST接口，或走数据库物化视图缓存方案。

##### 阶段3：过滤与数据增强
- **步骤描述**：对HIS返回行逐一检查扫码任务状态、处方状态等。
- **目的**：避免重复发药/退药，补充缺失的药品信息。
- **关联性**：依赖`ysf_sto_tc_task`、`ysf_sto_tc_status`等表判断扫码进度。
- **技术细节**：使用MapReduce聚合药品数量，计算`needScanFlag`字段。

##### 阶段4：返回结果
- **步骤描述**：封装`PrescriptionItem`列表返回前端。
- **目的**：前端渲染处方信息，支持选择发药或退药。
- **关联性**：后续`uploadScans`或`return`接口直接复用该数据结构。
- **技术细节**：返回分页信息，防止一次加载过多数据。

#### 数据流向
- 输入：`OutpatientPrescriptionQueryDto`
- 外部：调用HIS处方查询API
- 本地：查询`ysf_sto_tc_task`、`ysf_sto_dps_sub`过滤数据
- 输出：`OutpatientPrescriptionResponseVo`

---

### 2.2 住院发药查询（也可查退药）

#### 概述
住院发药查询接口为住院药房提供处方（领药单）查询能力，便于批量拆零、发药或退药处理，流程与门诊类似，但数据源与字段差异较大。

#### 关键业务规则
- 住院药房以领药单ID为主键，可能包含多病区药品需求。
- 拆零药需在发药前完成确认，退药需校验拆零确认记录。
- 查询结果需体现药品批次、库存信息，支持库存预警提示。

#### 业务流程生命周期
1. 前端提交`InpatientPrescriptionQueryDto`至`/queryInpatientPrescription`。
2. Service层调用HIS住院领药单接口，获取原始数据。
3. 按发药/退药逻辑过滤并标记拆零状态。
4. 返回`InpatientPrescriptionResponseVo`至前端。

##### 阶段1：查询请求
- **步骤描述**：前端选择病区、时间段，POST至后端。
- **目的**：获取待处理的住院领药单明细。
- **技术细节**：接口未加`@SaasAuthorizationVerify`，默认内部使用，可按需要补充。

##### 阶段2：调用HIS接口
- **步骤描述**：后端根据参数拼接HIS URL并调用。
- **目的**：同步最新领药单数据。
- **技术细节**：HIS接口返回JSON，包含批次号、药品规格等。

##### 阶段3：业务过滤与拆零标记
- **步骤描述**：过滤已确认拆零的药品，对未确认的打标提示。
- **目的**：确保拆零业务闭环。
- **关联性**：依赖`ysf_sto_tc_status`中`SdTcStatusEnum.INPATIENT_SPLIT`等状态。

##### 阶段4：结果返回
同门诊流程，返回前端渲染。

#### 数据流向
- 输入：`InpatientPrescriptionQueryDto`
- 外部：HIS住院领药单API
- 本地：`ysf_sto_tc_status` 拆零确认记录
- 输出：`InpatientPrescriptionResponseVo`

---

### 2.3 药品和追溯码的匹配关联上传

#### 概述
该接口用于药房工作人员扫码采集药品追溯码，并将采集结果与处方药品进行绑定，随后一次性上传到后端完成发药确认与平台销售（3505）报文生成。

#### 关键业务规则
- 同一追溯码只能绑定一次，不可重复上传。
- 处方药品行必须全部完成扫码后才能提交（可配置强制/非强制）。
- 支持整盒(`trdnFlag=0`)与拆零(`trdnFlag=1`)两种扫码模式。
- 上传成功后生成扫码任务记录与3505待上报记录。

#### 业务流程生命周期
1. 前端逐个扫码并实时校验追溯码格式。
2. 全部扫码完成后调用`/uploadScans`接口（`ysfService.uploadScans`）。
3. 只有通过所有的校验，才能保存数据，否则不保存数据。
4. 后端保存发药单(ysf_sto_dps)、明细(ysf_sto_dps_sub)与追溯码主表(ysf_sto_tc)。
5. 创建扫码任务表(ysf_sto_tc_task)及明细(ysf_sto_tc_task_sub)，生成3505报文表(nhsa_3505)。
6. 返回处理结果，前端提示发药成功，失败则提示失败原因。

##### 阶段1：扫码采集
- **步骤描述**：前端基于处方明细循环扫描，每扫一个追溯码即时校验重复性。
- **目的**：确保提交前数据完整、无重码。

##### 阶段2：调用上传接口
- **步骤描述**：前端封装`TraceabilityUploadDto`提交到`/uploadScans`。
- **目的**：触发后端批量持久化与业务处理。
- **技术细节**：接口需携带登录token；`fg_dps=0`表示发药。

##### 阶段3：持久化与任务生成
- **步骤描述**：Service按处方-药品-追溯码三层循环写入业务表。
- **目的**：建立追溯码与业务单据的映射关系。
- **关联性**：
  - `ysf_sto_dps` ←→ `ysf_sto_dps_sub` (1:N)
  - `ysf_sto_dps_sub` ←→ `ysf_sto_tc_task_sub` (1:N)
  - `ysf_sto_tc` ←→ `ysf_sto_tc_status` (1:N)
- **技术细节**：计算可拆零数量，更新`amount_rem`字段。

##### 阶段4：生成3505记录
- **步骤描述**：对成功发药的记录写入`nhsa_3505`表，标记`fg_up=0`待上传。
- **目的**：为后续定时任务上报医保平台做准备。

##### 阶段5：结果返回
返回`TraceabilityUploadResultVo`，包含成功/失败扫码信息。

#### 数据流向
- 输入：`TraceabilityUploadDto`
- 本地写表：`ysf_sto_dps`、`ysf_sto_dps_sub`、`ysf_sto_tc`、`ysf_sto_tc_status`、`ysf_sto_tc_task`、`ysf_sto_tc_task_sub`、`nhsa_3505`
- 输出：`TraceabilityUploadResultVo`

---

### 2.4 退药还原追溯码的使用状态

#### 概述
退药流程允许患者退回已发药品，并还原相应追溯码的使用状态。该流程支持部分退药或全部退药，主要涉及本地数据库更新和医保平台上报。整个流程从患者开具退药单开始，到后端接口处理并返回结果结束。

关键业务规则：
- 目前系统主要处理门诊退药（住院退药待扩展）。
- 退药时需区分发药单号，避免与原发药数据冲突。
- 拆零药品和整盒药品在追溯码剩余数量处理上不同。
- 所有操作需在事务中执行，确保数据一致性。

#### 退药流程生命周期
退药流程可分为前端处理、后端接口调用、数据更新和医保上报四个阶段。以下按时序详细说明。

##### 阶段1: 患者开具退药单
- **步骤描述**：患者先到医生处开具退药单，可为部分退药（仅退部分药品）或全部退药（退回所有药品）。
- **目的**：生成正式退药处方，作为后续查询和关联的基础。
- **关联性**：退药单包含处方ID（outPresId）和药品明细，用于前端查询和后端验证。
- **技术细节**：无直接数据库操作，此步骤在HIS系统中完成。

##### 阶段2: 前端查询与关联
- **步骤描述**：前端根据患者提供的退药信息查询退药单，并将退药药品与退药处方进行关联。
- **目的**：准备完整的退药数据包，确保药品追溯码与处方明细匹配。
- **关联性**：关联后形成TraceabilityUploadDto对象，包含处方列表、药品项和追溯码数组。
- **技术细节**：前端需验证追溯码格式（逗号分隔），并处理拆零标志（trdnFlag: 0为整盒，1为拆零）。

##### 阶段3: 调用后端退药接口
- **步骤描述**：前端将关联数据传输给后端"/return"接口（POST方法，请求体为TraceabilityUploadDto）。
- **目的**：触发后端业务逻辑处理退药请求。
- **关联性**：接口由FuYangRenYiController处理，调用ReturnDrugService.returnDrug方法。
- **技术细节**：接口需SaasAuthorizationVerify注解验证权限，返回ApiResult<ReturnDrugResultVo>包含成功/失败ID列表、消息和统计信息。

##### 阶段4: 后端数据更新与状态变化
后端处理每个处方项，按以下子步骤执行（在事务@Transactional中确保原子性）：

1. **创建/获取发药单（ysf_sto_dps）**：
   - **描述**：查询是否存在对应处方ID的发药单，若无则创建新记录。
   - **目的**：记录退药订单主信息，作为明细表的父记录。
   - **关联性**：ysf_sto_dps是主表，与ysf_sto_dps_sub（明细）通过id_dps关联。
   - **状态变化**：设置fg_status为FULL_RETURN（全部退药），fg_dps为"1"（发药单，但实际为退药标记）。
   - **技术细节**：填充患者ID、姓名、卡号、机构信息、创建/更新时间等字段。注意：需避免与原发药单冲突（TODO：优化单号生成）。

2. **处理每个药品明细（ysf_sto_dps_sub）**：
   - **描述**：为每个药品项创建/获取明细记录，存储追溯码数组和数量。
   - **目的**：记录退药药品的具体细节，包括价格、数量和单位。
   - **关联性**：与ysf_sto_dps关联，通过cfmxxh（处方明细ID）唯一标识。
   - **状态变化**：设置trac_cnt为追溯码数量，drugtracinfo为逗号分隔的追溯码字符串。
   - **技术细节**：处理原始HIS ID（ori_id, ori_cfmxxh）。

3. **处理每个追溯码（ysf_sto_tc 和 ysf_sto_tc_status）**：
   - **描述**：为每个追溯码更新/创建主记录，并创建状态记录。
   - **目的**：还原追溯码可用状态，记录退药历史。
   - **关联性**：ysf_sto_tc是追溯码主表，与ysf_sto_tc_status（状态历史）通过id_tc关联；状态记录链接到ysf_sto_dps_sub.id通过id_biz_ori。
   - **状态变化**：
     - ysf_sto_tc：设置amount_rem（剩余数量）——整盒设为包装系数（minDoseCount），拆零设为退药数量（quantity）；fg_active="1"（有效）。
     - ysf_sto_tc_status：设置sd_tc_status为OUTPATIENT_RETURN（门诊退药），fg_up="0"（未上传），fg_pack为拆零标志。
   - **技术细节**：拆零药（trdnFlag=1）只记录退药数量，整盒（=0）还原整个包装；退药不创建扫码任务表（ysf_sto_tc_task等）。

##### 阶段5: 医保上报与结果返回
- **步骤描述**：保存nhsa_3506数据后，实时调用3506接口上报退药信息。
- **目的**：同步退药数据到医保平台，确保合规。
- **关联性**：依赖前述本地数据更新成功；更新fg_up状态为已上传。
- **技术细节**：调用Nhsa3506Service.returnDrugAndSave3506Info，处理异常并收集成功/失败结果。
- **状态变化**：若成功，返回"退药处理成功，已同步医保平台"；失败记录错误消息。

#### 数据流向
- **输入**：TraceabilityUploadDto（处方列表、药品项、追溯码）。
- **输出**：ReturnDrugResultVo（成功/失败列表、统计）。
- **数据库交互**：插入/更新ysf_sto_dps、ysf_sto_dps_sub、ysf_sto_tc、ysf_sto_tc_status、nhsa_3506；调用nhsa_3506上报。
- **关键枚举**：
  - DispenseOrderStatusEnum：FULL_RETURN等。
  - SdTcStatusEnum：OUTPATIENT_RETURN等。

#### 注意事项与常见问题
- **事务管理**：所有数据库操作在事务中，若上报失败可能回滚本地变化（视配置）。
- **错误处理**：捕获BusinessException，确保部分失败不影响整体。
- **扩展点**：未来需添加住院退药区分（基于请求参数）。
- **调试提示**：检查日志中处方ID的处理细节，验证追溯码剩余数量计算。

---

### 2.5 定时上传3505报文

#### 概述
系统通过定时任务（`FuYangRenYiTask.uploadDataToPlatformTask`）批量上传本地生成的 *3505 销售报文* 至医保省平台，实现药品销售数据的监管上报。

#### 关键业务规则
1. 仅上传 `hsa_sync_status = "0"` 且 `drug_trac_info` **不为空** 的记录。
2. 同步前先调用 *追溯码信息同步* 流程，将 `ysf_sto_dps_sub` 已发药明细中的追溯码补充到 `nhsa_3505` 表。
3. 成功后将 `hsa_sync_status` 更新为 `"1"`；失败更新为 `"2"` 并记录错误原因。
4. 定时任务 Cron：`0 50 1/2 * * ?` —— 每两个小时的第 50 分钟执行一次。

#### 业务流程生命周期
定时上传共分为 *任务调度 → 追溯码同步 → 数据提取 → 报文组装与上传 → 状态更新 → 日志汇总* 六个阶段。

##### 阶段 1：任务调度
- **步骤描述**：Spring Scheduler 根据 Cron 表达式触发 `uploadDataToPlatformTask()`。
- **目的**：确保销售数据准实时上传至省平台。
- **实现**：`@Scheduled("0 50 1/2 * * ?")`, 每两个小时的第 50 分钟执行一次。

##### 阶段 2：追溯码同步
- **步骤描述**：调用 `Nhsa3505Service.syncDrugTraceInfo()`，将 `ysf_sto_dps_sub` 表中已发药明细的 `drugtracinfo` 补写入 `nhsa_3505.drug_trac_info`（仅当后者为空时）。
- **目的**：保证上传报文中包含完整的追溯码信息。

##### 阶段 3：数据提取
- **步骤描述**：`queryDataToUpload(null,null)` 查询 `nhsa_3505` 中满足关键规则的数据。
- **实现细节**：
  ```java
  LambdaQueryWrapper<Nhsa3505> qw = new LambdaQueryWrapper<>();
  qw.eq(Nhsa3505::getHsaSyncStatus, "0")
    .ne(Nhsa3505::getDrugTracInfo, "")
    .isNotNull(Nhsa3505::getDrugTracInfo);
  ```
- **说明**：当前实现一次性加载全部待传数据（未做分页），后续如数据量过大可考虑批处理优化。

##### 阶段 4：报文组装与上传
- **步骤描述**：
  1. 获取签名号：`NhsaHttpUtil.fsi9001()` → `sign_no`。
  2. 对每条 `Nhsa3505` 记录调用 `buildSelinfo3505` 组装 `Selinfo3505` 报文对象；若缺失生产/有效期则自动填充 *去年今日* / *明年今日* 默认值。
  3. 调用 `NhsaHttpUtil.fsi3505(signNo, selinfo3505, nhsaAccount)` 上传。
- **异常处理**：捕获接口或业务异常，记录失败信息，上传不做重试（后续可扩展退避重试策略）。

##### 阶段 5：状态更新
- **成功**：`hsa_sync_status = "1"`，`hsa_sync_time = now()`，`hsa_sync_remark = "上传两定接口成功{时间}"`。
- **失败**：`hsa_sync_status = "2"`，`hsa_sync_remark = "上传失败：{err_msg}"`。
- **说明**：当前实现未直接联动 `ysf_sto_tc_status` 表，后续若需保持追溯码状态一致，可在此阶段新增更新逻辑。

##### 阶段 6：日志汇总
- **自动任务**：输出“总数 / 成功 / 失败”统计日志，便于运维监控。
- **手动触发**：`manualUploadDataToPlatform` 接口返回详细的统计与错误列表，供前端或运维查看。
- **统计表**：`nhsa_3505_cnt_count` 及 `nhsa_3505_ymf_user_count` 的每日统计由独立的 SuperSet 定时任务完成，不在本流程内。

#### 数据流向
- **输入**：定时触发，无外部请求。
- **本地表**：
  - 读取 `nhsa_3505`（销售报文）；
  - 必要时更新 `nhsa_3505` 字段。
- **外部接口**：
  - `fsi9001` 获取签名号；
  - `fsi3505` 上传销售报文。
- **输出**：更新 `hsa_sync_status/remark/time`，记录成功或失败日志。

---


## 3.数据库业务表的设计思想

业务表是按照主子表的模式设计,成对的出现,并记录业务信息

- 发药单（ysf_sto_dps）和 发药单明细（ysf_sto_dps_sub）

- 追溯码表（ysf_sto_tc）和追溯码状态表（ysf_sto_tc_status）

- 扫码任务表（ysf_sto_tc_task）和扫码任务明细表（ysf_sto_tc_task_sub）


单独业务记录表

- 上传3505销售报文表（nhsa_3505）

- 上传3506退药报文表（nhsa_3506）

- 药房发药信息统计表（nhsa3505_ymf_user_count）

- 上传3505销售报文统计表（nhsa_3505_cnt_count）


## 4. 同步药品字典

- 从his获取的药品字典信息，每天同步一次，拼装成saas接口的药品实体对象后，每次200条分批上传到saas平台。

## 5. 同步发药时间

- 扫描上传的药品信息，默认是没有写入发药时间，因为可能会存在发药时间不一致的情况，所以需要同步发药时间。

- 执行定时任务时取his接口返回的发药时间字段，更新ysf_sto_dps.send_time字段。
